/**
 * 结算工具模块 - 云函数版本
 * 负责处理结算相关的工具函数
 */
/**
 * 从结算详情中解析玩家分数调整信息
 * @param {Object} settlementMessage 结算消息对象
 * @param {Array} players 玩家列表 [{user_id, name}]
 * @param {Object} context 上下文信息，包含茶水余额等
 * @returns {Object} 分数调整映射 { playerId: adjustment, tea_water: teaAdjustment }
 */
function parseSettlementAdjustments(settlementMessage, players, context = {}) {
  const adjustments = {}
  
  // 使用保存的分数调整信息
  if (settlementMessage.detail_data && settlementMessage.detail_data.score_adjustments && Array.isArray(settlementMessage.detail_data.score_adjustments)) {
    console.log('云函数解析结算分数调整信息');
    settlementMessage.detail_data.score_adjustments.forEach(adjustment => {
      if (adjustment.playerId && typeof adjustment.adjustment === 'number') {
        adjustments[adjustment.playerId] = adjustment.adjustment;
        if (adjustment.playerId === 'tea_water') {
          console.log(`茶水分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        } else {
          console.log(`玩家${adjustment.playerId}分数调整: ${adjustment.adjustment} (${adjustment.reason})`);
        }
      }
    });
  } else {
    console.warn('结算消息中缺少分数调整信息');
  }
  
  return adjustments;
}

module.exports = {
  parseSettlementAdjustments
};
